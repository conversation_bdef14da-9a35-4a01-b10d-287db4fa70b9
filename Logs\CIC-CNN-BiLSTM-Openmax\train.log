2024-08-02 14:54:03,242 - INFO - Starting training...
2024-08-02 14:54:06,067 - INFO - Epoch 1/30, Train Loss: 1.4280, Train Acc: 68.94%,         Val Acc: 67.66%, val_total: 4499, val_correct: 3044
2024-08-02 14:54:07,818 - INFO - Epoch 2/30, Train Loss: 0.6350, Train Acc: 94.84%,         Val Acc: 97.67%, val_total: 4499, val_correct: 4394
2024-08-02 14:54:09,648 - INFO - Epoch 3/30, Train Loss: 0.1785, Train Acc: 98.02%,         Val Acc: 98.80%, val_total: 4499, val_correct: 4445
2024-08-02 14:54:11,447 - INFO - Epoch 4/30, Train Loss: 0.0635, Train Acc: 99.05%,         Val Acc: 99.40%, val_total: 4499, val_correct: 4472
2024-08-02 14:54:13,253 - INFO - Epoch 5/30, Train Loss: 0.0369, Train Acc: 99.37%,         Val Acc: 99.51%, val_total: 4499, val_correct: 4477
2024-08-02 14:54:15,064 - INFO - Epoch 6/30, Train Loss: 0.0262, Train Acc: 99.59%,         Val Acc: 99.58%, val_total: 4499, val_correct: 4480
2024-08-02 14:54:16,809 - INFO - Epoch 7/30, Train Loss: 0.0212, Train Acc: 99.63%,         Val Acc: 99.62%, val_total: 4499, val_correct: 4482
2024-08-02 14:54:18,617 - INFO - Epoch 8/30, Train Loss: 0.0180, Train Acc: 99.72%,         Val Acc: 99.76%, val_total: 4499, val_correct: 4488
2024-08-02 14:54:20,374 - INFO - Epoch 9/30, Train Loss: 0.0158, Train Acc: 99.75%,         Val Acc: 99.69%, val_total: 4499, val_correct: 4485
2024-08-02 14:54:22,127 - INFO - Epoch 10/30, Train Loss: 0.0145, Train Acc: 99.75%,         Val Acc: 99.71%, val_total: 4499, val_correct: 4486
2024-08-02 14:54:23,963 - INFO - Epoch 11/30, Train Loss: 0.0134, Train Acc: 99.78%,         Val Acc: 99.73%, val_total: 4499, val_correct: 4487
2024-08-02 14:54:25,717 - INFO - Epoch 12/30, Train Loss: 0.0118, Train Acc: 99.80%,         Val Acc: 99.73%, val_total: 4499, val_correct: 4487
2024-08-02 14:54:27,658 - INFO - Epoch 13/30, Train Loss: 0.0099, Train Acc: 99.87%,         Val Acc: 99.78%, val_total: 4499, val_correct: 4489
2024-08-02 14:54:29,417 - INFO - Epoch 14/30, Train Loss: 0.0095, Train Acc: 99.86%,         Val Acc: 99.80%, val_total: 4499, val_correct: 4490
2024-08-02 14:54:31,355 - INFO - Epoch 15/30, Train Loss: 0.0089, Train Acc: 99.87%,         Val Acc: 99.80%, val_total: 4499, val_correct: 4490
2024-08-02 14:54:33,211 - INFO - Epoch 16/30, Train Loss: 0.0082, Train Acc: 99.88%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:35,152 - INFO - Epoch 17/30, Train Loss: 0.0083, Train Acc: 99.86%,         Val Acc: 99.80%, val_total: 4499, val_correct: 4490
2024-08-02 14:54:37,144 - INFO - Epoch 18/30, Train Loss: 0.0079, Train Acc: 99.86%,         Val Acc: 99.80%, val_total: 4499, val_correct: 4490
2024-08-02 14:54:39,161 - INFO - Epoch 19/30, Train Loss: 0.0072, Train Acc: 99.89%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:41,258 - INFO - Epoch 20/30, Train Loss: 0.0068, Train Acc: 99.89%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:43,586 - INFO - Epoch 21/30, Train Loss: 0.0073, Train Acc: 99.89%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:45,904 - INFO - Epoch 22/30, Train Loss: 0.0070, Train Acc: 99.88%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:48,149 - INFO - Epoch 23/30, Train Loss: 0.0063, Train Acc: 99.91%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:50,430 - INFO - Epoch 24/30, Train Loss: 0.0060, Train Acc: 99.91%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:52,713 - INFO - Epoch 25/30, Train Loss: 0.0062, Train Acc: 99.92%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:55,013 - INFO - Epoch 26/30, Train Loss: 0.0058, Train Acc: 99.90%,         Val Acc: 99.84%, val_total: 4499, val_correct: 4492
2024-08-02 14:54:57,720 - INFO - Epoch 27/30, Train Loss: 0.0057, Train Acc: 99.91%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:54:59,997 - INFO - Epoch 28/30, Train Loss: 0.0056, Train Acc: 99.93%,         Val Acc: 99.82%, val_total: 4499, val_correct: 4491
2024-08-02 14:55:02,553 - INFO - Epoch 29/30, Train Loss: 0.0054, Train Acc: 99.93%,         Val Acc: 99.84%, val_total: 4499, val_correct: 4492
2024-08-02 14:55:04,838 - INFO - Epoch 30/30, Train Loss: 0.0052, Train Acc: 99.92%,         Val Acc: 99.84%, val_total: 4499, val_correct: 4492
2024-08-02 14:55:05,270 - INFO - Fittting Weibull distribution...
2024-08-02 14:55:14,128 - INFO - Evaluation...
2024-08-02 14:55:16,270 - INFO - Softmax accuracy is 0.487
2024-08-02 14:55:16,270 - INFO - Softmax precision is 0.297
2024-08-02 14:55:16,271 - INFO - Softmax recall is 0.487
2024-08-02 14:55:16,271 - INFO - Softmax F1 is 0.487
2024-08-02 14:55:16,272 - INFO - Softmax f1_macro is 0.618
2024-08-02 14:55:16,272 - INFO - Softmax f1_macro_weighted is 0.351
2024-08-02 14:55:16,272 - INFO - Softmax area_under_roc is 0.995
2024-08-02 14:55:16,272 - INFO - Softmax classification_report:
{'0': {'precision': 0.3578005115089514, 'recall': 0.9992857142857143, 'f1-score': 0.5269303201506591, 'support': 1400.0}, '1': {'precision': 0.8663366336633663, 'recall': 1.0, 'f1-score': 0.9283819628647215, 'support': 1400.0}, '2': {'precision': 0.36430138990490124, 'recall': 0.996, 'f1-score': 0.533476164970541, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 1.0, 'f1-score': 1.0, 'support': 450.0}, '4': {'precision': 0.2716723549488055, 'recall': 0.9974937343358395, 'f1-score': 0.42703862660944203, 'support': 399.0}, '5': {'precision': 0.831353919239905, 'recall': 1.0, 'f1-score': 0.9079118028534371, 'support': 350.0}, '6': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 4730.0}, 'accuracy': 0.4870516849062737, 'macro avg': {'precision': 0.5273521156094185, 'recall': 0.8561113498030791, 'f1-score': 0.6176769824926859, 'support': 9229.0}, 'weighted avg': {'precision': 0.29746601360409963, 'recall': 0.4870516849062737, 'f1-score': 0.3513200586978734, 'support': 9229.0}}
2024-08-02 14:55:16,273 - INFO - _________________________________________
2024-08-02 14:55:16,274 - INFO - SoftmaxThreshold accuracy is 0.658
2024-08-02 14:55:16,274 - INFO - SoftmaxThreshold precision is 0.851
2024-08-02 14:55:16,274 - INFO - SoftmaxThreshold recall is 0.658
2024-08-02 14:55:16,274 - INFO - SoftmaxThreshold F1 is 0.658
2024-08-02 14:55:16,274 - INFO - SoftmaxThreshold f1_macro is 0.781
2024-08-02 14:55:16,275 - INFO - SoftmaxThreshold f1_macro_weighted is 0.641
2024-08-02 14:55:16,275 - INFO - SoftmaxThreshold area_under_roc is 0.995
2024-08-02 14:55:16,275 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.3832876712328767, 'recall': 0.9992857142857143, 'f1-score': 0.554059405940594, 'support': 1400.0}, '1': {'precision': 0.9079118028534371, 'recall': 1.0, 'f1-score': 0.9517335146159076, 'support': 1400.0}, '2': {'precision': 0.4353146853146853, 'recall': 0.996, 'f1-score': 0.6058394160583941, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 1.0, 'f1-score': 1.0, 'support': 450.0}, '4': {'precision': 0.8614718614718615, 'recall': 0.9974937343358395, 'f1-score': 0.9245063879210221, 'support': 399.0}, '5': {'precision': 0.8684863523573201, 'recall': 1.0, 'f1-score': 0.9296148738379814, 'support': 350.0}, '6': {'precision': 0.9993662864385298, 'recall': 0.33340380549682874, 'f1-score': 0.5, 'support': 4730.0}, 'accuracy': 0.6579261025029798, 'macro avg': {'precision': 0.7794055228098158, 'recall': 0.9037404648740547, 'f1-score': 0.7808219426248427, 'support': 9229.0}, 'weighted avg': {'precision': 0.8505836642414955, 'recall': 0.6579261025029798, 'f1-score': 0.6414858653626699, 'support': 9229.0}}
2024-08-02 14:55:16,276 - INFO - _________________________________________
2024-08-02 14:55:16,276 - INFO - OpenMax accuracy is 0.849
2024-08-02 14:55:16,277 - INFO - OpenMax precision is 0.871
2024-08-02 14:55:16,277 - INFO - OpenMax recall is 0.849
2024-08-02 14:55:16,277 - INFO - OpenMax F1 is 0.849
2024-08-02 14:55:16,277 - INFO - OpenMax f1_macro is 0.881
2024-08-02 14:55:16,277 - INFO - OpenMax f1_macro_weighted is 0.855
2024-08-02 14:55:16,277 - INFO - OpenMax area_under_roc is 0.954
2024-08-02 14:55:16,278 - INFO - OpenMax classification_report:
{'0': {'precision': 0.5985808413583376, 'recall': 0.8435714285714285, 'f1-score': 0.7002668247850578, 'support': 1400.0}, '1': {'precision': 1.0, 'recall': 0.9142857142857143, 'f1-score': 0.955223880597015, 'support': 1400.0}, '2': {'precision': 1.0, 'recall': 0.732, 'f1-score': 0.8452655889145496, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 0.9911111111111112, 'f1-score': 0.9955357142857144, 'support': 450.0}, '4': {'precision': 0.9967320261437909, 'recall': 0.7644110275689223, 'f1-score': 0.8652482269503546, 'support': 399.0}, '5': {'precision': 1.0, 'recall': 0.9142857142857143, 'f1-score': 0.955223880597015, 'support': 350.0}, '6': {'precision': 0.8677831643895989, 'recall': 0.8325581395348837, 'f1-score': 0.8498057833405265, 'support': 4730.0}, 'accuracy': 0.8490627370245963, 'macro avg': {'precision': 0.9232994331273897, 'recall': 0.8560318764796822, 'f1-score': 0.880938557067176, 'support': 9229.0}, 'weighted avg': {'precision': 0.8712020396463157, 'recall': 0.8490627370245963, 'f1-score': 0.8546380549770923, 'support': 9229.0}}
2024-08-02 14:55:16,278 - INFO - _________________________________________
